import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/f_trade_config_model.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/f_trade_config_model.g.dart';

@JsonSerializable()
class FTradeConfigModel {
  /// 主键ID
  int id = 0;

  /// 市场
  String market = '';

  /// 保证金比例
  double marginRatio = 0.0;

  /// 平仓线百分比
  double closeRadio = 0.0;

  /// 货币
  String currency = '';

  /// 最大空匹配量
  double maxEmptyMatch = 0.0;

  /// 最小空匹配量
  double minEmptyMatch = 0.0;

  /// 最大匹配量
  double maxManyMatch = 0.0;

  /// 最小匹配量
  double minManyMatch = 0.0;

  /// 最大涨幅
  double maxIncrease = 0.0;

  /// 最小涨幅
  double minIncrease = 0.0;

  /// 最大交易量
  double maxTradeQuantity = 0.0;

  /// 最小交易量
  double minTradeQuantity = 0.0;

  /// 乘数
  int multiple = 0;

  /// 乘数列表 (leverage multiplier)
  List<String> multipleList = [];

  /// 返佣状态
  int rebateStatus = 0;

  /// 反向交易状态
  int reverseTradeStatus = 0;

  /// 卖出模式
  int sellModel = 0;

  /// 卖值
  int sellValue = 0;

  /// 状态
  bool status = false;

  /// 符号
  String symbol = '';

  /// 交易模式
  int tradeModel = 0;

  /// 预警线百分比
  double warnRadio = 0.0;

  FTradeConfigModel();

  factory FTradeConfigModel.fromJson(Map<String, dynamic> json) => $FTradeConfigModelFromJson(json);

  Map<String, dynamic> toJson() => $FTradeConfigModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
