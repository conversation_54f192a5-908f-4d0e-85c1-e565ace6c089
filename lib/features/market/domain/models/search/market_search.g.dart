// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'market_search.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MarketSearchResponseImpl _$$MarketSearchResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$MarketSearchResponseImpl(
      code: (json['code'] as num?)?.toInt(),
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => MarketSearchData.fromJson(e as Map<String, dynamic>))
          .toList(),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$MarketSearchResponseImplToJson(
        _$MarketSearchResponseImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$MarketSearchDataImpl _$$MarketSearchDataImplFromJson(
        Map<String, dynamic> json) =>
    _$MarketSearchDataImpl(
      currency: json['currency'] as String?,
      instrument: json['instrument'] as String?,
      lotSize: (json['lotSize'] as num?)?.toInt(),
      market: json['market'] as String?,
      name: json['name'] as String?,
      securityType: json['securityType'] as String?,
      symbol: json['symbol'] as String?,
      tickSize: (json['tickSize'] as num?)?.toDouble(),
      isWatchlist: json['isWatchlist'] as bool? ?? false,
    );

Map<String, dynamic> _$$MarketSearchDataImplToJson(
        _$MarketSearchDataImpl instance) =>
    <String, dynamic>{
      'currency': instance.currency,
      'instrument': instance.instrument,
      'lotSize': instance.lotSize,
      'market': instance.market,
      'name': instance.name,
      'securityType': instance.securityType,
      'symbol': instance.symbol,
      'tickSize': instance.tickSize,
      'isWatchlist': instance.isWatchlist,
    };
