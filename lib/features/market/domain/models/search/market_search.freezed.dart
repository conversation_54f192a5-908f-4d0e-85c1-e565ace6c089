// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'market_search.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MarketSearchResponse _$MarketSearchResponseFromJson(Map<String, dynamic> json) {
  return _MarketSearchResponse.fromJson(json);
}

/// @nodoc
mixin _$MarketSearchResponse {
  int? get code => throw _privateConstructorUsedError;
  List<MarketSearchData>? get data => throw _privateConstructorUsedError;
  String? get msg => throw _privateConstructorUsedError;

  /// Serializes this MarketSearchResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MarketSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MarketSearchResponseCopyWith<MarketSearchResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MarketSearchResponseCopyWith<$Res> {
  factory $MarketSearchResponseCopyWith(MarketSearchResponse value,
          $Res Function(MarketSearchResponse) then) =
      _$MarketSearchResponseCopyWithImpl<$Res, MarketSearchResponse>;
  @useResult
  $Res call({int? code, List<MarketSearchData>? data, String? msg});
}

/// @nodoc
class _$MarketSearchResponseCopyWithImpl<$Res,
        $Val extends MarketSearchResponse>
    implements $MarketSearchResponseCopyWith<$Res> {
  _$MarketSearchResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MarketSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
  }) {
    return _then(_value.copyWith(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<MarketSearchData>?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MarketSearchResponseImplCopyWith<$Res>
    implements $MarketSearchResponseCopyWith<$Res> {
  factory _$$MarketSearchResponseImplCopyWith(_$MarketSearchResponseImpl value,
          $Res Function(_$MarketSearchResponseImpl) then) =
      __$$MarketSearchResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? code, List<MarketSearchData>? data, String? msg});
}

/// @nodoc
class __$$MarketSearchResponseImplCopyWithImpl<$Res>
    extends _$MarketSearchResponseCopyWithImpl<$Res, _$MarketSearchResponseImpl>
    implements _$$MarketSearchResponseImplCopyWith<$Res> {
  __$$MarketSearchResponseImplCopyWithImpl(_$MarketSearchResponseImpl _value,
      $Res Function(_$MarketSearchResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of MarketSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
  }) {
    return _then(_$MarketSearchResponseImpl(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<MarketSearchData>?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MarketSearchResponseImpl implements _MarketSearchResponse {
  const _$MarketSearchResponseImpl(
      {this.code, final List<MarketSearchData>? data, this.msg})
      : _data = data;

  factory _$MarketSearchResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$MarketSearchResponseImplFromJson(json);

  @override
  final int? code;
  final List<MarketSearchData>? _data;
  @override
  List<MarketSearchData>? get data {
    final value = _data;
    if (value == null) return null;
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? msg;

  @override
  String toString() {
    return 'MarketSearchResponse(code: $code, data: $data, msg: $msg)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MarketSearchResponseImpl &&
            (identical(other.code, code) || other.code == code) &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.msg, msg) || other.msg == msg));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, code, const DeepCollectionEquality().hash(_data), msg);

  /// Create a copy of MarketSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MarketSearchResponseImplCopyWith<_$MarketSearchResponseImpl>
      get copyWith =>
          __$$MarketSearchResponseImplCopyWithImpl<_$MarketSearchResponseImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MarketSearchResponseImplToJson(
      this,
    );
  }
}

abstract class _MarketSearchResponse implements MarketSearchResponse {
  const factory _MarketSearchResponse(
      {final int? code,
      final List<MarketSearchData>? data,
      final String? msg}) = _$MarketSearchResponseImpl;

  factory _MarketSearchResponse.fromJson(Map<String, dynamic> json) =
      _$MarketSearchResponseImpl.fromJson;

  @override
  int? get code;
  @override
  List<MarketSearchData>? get data;
  @override
  String? get msg;

  /// Create a copy of MarketSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MarketSearchResponseImplCopyWith<_$MarketSearchResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

MarketSearchData _$MarketSearchDataFromJson(Map<String, dynamic> json) {
  return _MarketSearchData.fromJson(json);
}

/// @nodoc
mixin _$MarketSearchData {
  String? get currency => throw _privateConstructorUsedError;
  String? get instrument => throw _privateConstructorUsedError;
  int? get lotSize => throw _privateConstructorUsedError;
  String? get market => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get securityType => throw _privateConstructorUsedError;
  String? get symbol => throw _privateConstructorUsedError;
  double? get tickSize => throw _privateConstructorUsedError;
  bool get isWatchlist => throw _privateConstructorUsedError;

  /// Serializes this MarketSearchData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MarketSearchData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MarketSearchDataCopyWith<MarketSearchData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MarketSearchDataCopyWith<$Res> {
  factory $MarketSearchDataCopyWith(
          MarketSearchData value, $Res Function(MarketSearchData) then) =
      _$MarketSearchDataCopyWithImpl<$Res, MarketSearchData>;
  @useResult
  $Res call(
      {String? currency,
      String? instrument,
      int? lotSize,
      String? market,
      String? name,
      String? securityType,
      String? symbol,
      double? tickSize,
      bool isWatchlist});
}

/// @nodoc
class _$MarketSearchDataCopyWithImpl<$Res, $Val extends MarketSearchData>
    implements $MarketSearchDataCopyWith<$Res> {
  _$MarketSearchDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MarketSearchData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currency = freezed,
    Object? instrument = freezed,
    Object? lotSize = freezed,
    Object? market = freezed,
    Object? name = freezed,
    Object? securityType = freezed,
    Object? symbol = freezed,
    Object? tickSize = freezed,
    Object? isWatchlist = null,
  }) {
    return _then(_value.copyWith(
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      instrument: freezed == instrument
          ? _value.instrument
          : instrument // ignore: cast_nullable_to_non_nullable
              as String?,
      lotSize: freezed == lotSize
          ? _value.lotSize
          : lotSize // ignore: cast_nullable_to_non_nullable
              as int?,
      market: freezed == market
          ? _value.market
          : market // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      securityType: freezed == securityType
          ? _value.securityType
          : securityType // ignore: cast_nullable_to_non_nullable
              as String?,
      symbol: freezed == symbol
          ? _value.symbol
          : symbol // ignore: cast_nullable_to_non_nullable
              as String?,
      tickSize: freezed == tickSize
          ? _value.tickSize
          : tickSize // ignore: cast_nullable_to_non_nullable
              as double?,
      isWatchlist: null == isWatchlist
          ? _value.isWatchlist
          : isWatchlist // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MarketSearchDataImplCopyWith<$Res>
    implements $MarketSearchDataCopyWith<$Res> {
  factory _$$MarketSearchDataImplCopyWith(_$MarketSearchDataImpl value,
          $Res Function(_$MarketSearchDataImpl) then) =
      __$$MarketSearchDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? currency,
      String? instrument,
      int? lotSize,
      String? market,
      String? name,
      String? securityType,
      String? symbol,
      double? tickSize,
      bool isWatchlist});
}

/// @nodoc
class __$$MarketSearchDataImplCopyWithImpl<$Res>
    extends _$MarketSearchDataCopyWithImpl<$Res, _$MarketSearchDataImpl>
    implements _$$MarketSearchDataImplCopyWith<$Res> {
  __$$MarketSearchDataImplCopyWithImpl(_$MarketSearchDataImpl _value,
      $Res Function(_$MarketSearchDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of MarketSearchData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currency = freezed,
    Object? instrument = freezed,
    Object? lotSize = freezed,
    Object? market = freezed,
    Object? name = freezed,
    Object? securityType = freezed,
    Object? symbol = freezed,
    Object? tickSize = freezed,
    Object? isWatchlist = null,
  }) {
    return _then(_$MarketSearchDataImpl(
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      instrument: freezed == instrument
          ? _value.instrument
          : instrument // ignore: cast_nullable_to_non_nullable
              as String?,
      lotSize: freezed == lotSize
          ? _value.lotSize
          : lotSize // ignore: cast_nullable_to_non_nullable
              as int?,
      market: freezed == market
          ? _value.market
          : market // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      securityType: freezed == securityType
          ? _value.securityType
          : securityType // ignore: cast_nullable_to_non_nullable
              as String?,
      symbol: freezed == symbol
          ? _value.symbol
          : symbol // ignore: cast_nullable_to_non_nullable
              as String?,
      tickSize: freezed == tickSize
          ? _value.tickSize
          : tickSize // ignore: cast_nullable_to_non_nullable
              as double?,
      isWatchlist: null == isWatchlist
          ? _value.isWatchlist
          : isWatchlist // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MarketSearchDataImpl extends _MarketSearchData {
  const _$MarketSearchDataImpl(
      {this.currency,
      this.instrument,
      this.lotSize,
      this.market,
      this.name,
      this.securityType,
      this.symbol,
      this.tickSize,
      this.isWatchlist = false})
      : super._();

  factory _$MarketSearchDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$MarketSearchDataImplFromJson(json);

  @override
  final String? currency;
  @override
  final String? instrument;
  @override
  final int? lotSize;
  @override
  final String? market;
  @override
  final String? name;
  @override
  final String? securityType;
  @override
  final String? symbol;
  @override
  final double? tickSize;
  @override
  @JsonKey()
  final bool isWatchlist;

  @override
  String toString() {
    return 'MarketSearchData(currency: $currency, instrument: $instrument, lotSize: $lotSize, market: $market, name: $name, securityType: $securityType, symbol: $symbol, tickSize: $tickSize, isWatchlist: $isWatchlist)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MarketSearchDataImpl &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.instrument, instrument) ||
                other.instrument == instrument) &&
            (identical(other.lotSize, lotSize) || other.lotSize == lotSize) &&
            (identical(other.market, market) || other.market == market) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.securityType, securityType) ||
                other.securityType == securityType) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.tickSize, tickSize) ||
                other.tickSize == tickSize) &&
            (identical(other.isWatchlist, isWatchlist) ||
                other.isWatchlist == isWatchlist));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, currency, instrument, lotSize,
      market, name, securityType, symbol, tickSize, isWatchlist);

  /// Create a copy of MarketSearchData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MarketSearchDataImplCopyWith<_$MarketSearchDataImpl> get copyWith =>
      __$$MarketSearchDataImplCopyWithImpl<_$MarketSearchDataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MarketSearchDataImplToJson(
      this,
    );
  }
}

abstract class _MarketSearchData extends MarketSearchData {
  const factory _MarketSearchData(
      {final String? currency,
      final String? instrument,
      final int? lotSize,
      final String? market,
      final String? name,
      final String? securityType,
      final String? symbol,
      final double? tickSize,
      final bool isWatchlist}) = _$MarketSearchDataImpl;
  const _MarketSearchData._() : super._();

  factory _MarketSearchData.fromJson(Map<String, dynamic> json) =
      _$MarketSearchDataImpl.fromJson;

  @override
  String? get currency;
  @override
  String? get instrument;
  @override
  int? get lotSize;
  @override
  String? get market;
  @override
  String? get name;
  @override
  String? get securityType;
  @override
  String? get symbol;
  @override
  double? get tickSize;
  @override
  bool get isWatchlist;

  /// Create a copy of MarketSearchData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MarketSearchDataImplCopyWith<_$MarketSearchDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
