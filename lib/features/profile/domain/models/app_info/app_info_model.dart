import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_info_model.freezed.dart';
part 'app_info_model.g.dart';

@freezed
class AppInfoModel with _$AppInfoModel {
  const factory AppInfoModel({
    @Default("") String content,
    @Default(0) int id,
    @Default("") String title,
    @Default("") String sealImageUrl,
    @Default(0) int type,
  }) = _AppInfoModel;

  factory AppInfoModel.fromJson(Map<String, dynamic> json) => _$AppInfoModelFromJson(json);
}
